import React, { useState } from "react";
import Link from "next/link";
import { Logo } from "../logo";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { ChevronUp, ChevronDown, Home, ZoomIn, ZoomOut, Maximize2 } from "lucide-react";
import { checkOS } from "@/lib/utils-os";

interface HeaderDropdownProps {
	onZoomIn?: () => void;
	onZoomOut?: () => void;
	onZoomToFit?: () => void;
}

export const HeaderDropdown: React.FC<HeaderDropdownProps> = ({ onZoomIn, onZoomOut, onZoomToFit }) => {
	const [isOpen, setIsOpen] = useState(false);
	const isMac = checkOS("Mac");
	const modifierKey = isMac ? "⌘" : "Ctrl";

	return (
		<div className="absolute top-4 left-4 z-20">
			<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
				<DropdownMenuTrigger asChild>
					<button className="bg-card hover:bg-accent flex flex-row items-center gap-2 rounded-lg p-2 transition-colors">
						<Logo className="size-6" />
						{isOpen ? <ChevronDown className="text-muted-foreground size-4" /> : <ChevronUp className="text-muted-foreground size-4" />}
					</button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="start" className="w-48 space-y-1 p-2">
					<DropdownMenuItem asChild>
						<Link href="/" className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600">
							<span>Home</span>
						</Link>
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={onZoomToFit} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom to fit</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">⇧</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">1</div>
						</div>
					</DropdownMenuItem>
					<DropdownMenuItem onClick={onZoomIn} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom in</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">+</div>
						</div>
					</DropdownMenuItem>
					<DropdownMenuItem onClick={onZoomOut} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom out</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">-</div>
						</div>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
};
